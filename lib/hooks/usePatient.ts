'use client';

import { useState, useEffect, useCallback } from 'react';
import { getPatientById, updatePatient } from '@/lib/services/patientService';
import { Patient } from '@/lib/types';
import { useUser } from './useUser';

interface UsePatientReturn {
  patient: Patient | null;
  loading: boolean;
  error: string | null;
  updatePatientData: (patientId: string, updatedData: Partial<Patient>) => Promise<void>;
  refetchPatient: () => Promise<void>;
}

export function usePatient(patientId: string | null): UsePatientReturn {
  const [patient, setPatient] = useState<Patient | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { userProfile, isAdmin } = useUser();

  const fetchPatient = useCallback(async () => {
    if (!patientId) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const patientData = await getPatientById(patientId);
      
      if (!patientData) {
        setError('Hasta bulunamadı');
        setPatient(null);
      } else if (userProfile && patientData.doctorId !== userProfile.id && !isAdmin) {
        // Security check: ensure the patient belongs to the current doctor (unless user is admin)
        setError('Bu hastaya erişim yetkiniz yok');
        setPatient(null);
      } else {
        setPatient(patientData);
      }
    } catch (err) {
      console.error('Error fetching patient:', err);
      setError('Hasta bilgileri yüklenirken bir hata oluştu');
      setPatient(null);
    } finally {
      setLoading(false);
    }
  }, [patientId, userProfile, isAdmin]);

  const updatePatientData = async (patientId: string, updatedData: Partial<Patient>) => {
    try {
      setError(null);
      await updatePatient(patientId, updatedData);
      
      // Refetch patient data to get the updated information
      await fetchPatient();
    } catch (err) {
      console.error('Error updating patient:', err);
      setError('Hasta bilgileri güncellenirken bir hata oluştu');
      throw err;
    }
  };

  const refetchPatient = async () => {
    await fetchPatient();
  };

  useEffect(() => {
    if (userProfile) {
      fetchPatient();
    }
  }, [patientId, userProfile, fetchPatient]);

  return {
    patient,
    loading,
    error,
    updatePatientData,
    refetchPatient,
  };
} 